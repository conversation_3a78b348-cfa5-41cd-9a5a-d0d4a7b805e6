import { ReactNode } from "react";
import { Link } from "../components/Link";
import logoUrl from "../assets/logo.svg";

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen bg-[] flex flex-col  text-gray-900">
      <Header />
      <main className="flex flex-1 max-w-6xl mx-auto w-full px-4 py-6">
        <div className="w-full">{children}</div>
      </main>
      <Footer />
    </div>
  );
}

function Header() {
  return (
    <header className="border-b shadow-sm">
      <nav>
        {/* logo */}
        <div>
           <Link href="/">
            <img src={logoUrl} height={64} width={64} alt="logo" />
          </Link>
        </div>

        {/* page nav items /menu*/}
      </nav>
    </header>
  );
}

function Footer() {
  return (
    <footer className="border-t bg-gray-50 mt-auto">
      <div className="max-w-6xl mx-auto px-4 py-6 text-sm text-center text-gray-500">
        © {new Date().getFullYear()} Fadhili. Built with ❤️ in Kenya.
      </div>
    </footer>
  );
}
