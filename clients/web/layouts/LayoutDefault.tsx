import { ReactNode } from "react";
import { Link } from "../components/Link";
import logoUrl from "../assets/logo.svg";

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen flex flex-col bg-white text-gray-900">
      <Header />
      <main className="flex flex-1 max-w-6xl mx-auto w-full px-4 py-6">
        <div className="w-full">{children}</div>
      </main>
      <Footer />
    </div>
  );
}

function Header() {
  return (
    <header className="border-b bg-gray-50 shadow-sm">
      <div className="max-w-6xl mx-auto px-4 py-4 flex items-center justify-between">
        <a href="/" className="flex items-center gap-2">
          <img src={logoUrl} alt="logo" className="w-8 h-8" />
          <span className="text-lg font-bold tracking-wide">Fadhili</span>
        </a>
        <nav className="flex gap-6 text-sm font-medium">
          <Link href="/">Home</Link>
          <Link href="/community">Community</Link>
          <Link href="/blog">Blog</Link>
          <Link href="/about">About</Link>
        </nav>
      </div>
    </header>
  );
}

function Footer() {
  return (
    <footer className="border-t bg-gray-50 mt-auto">
      <div className="max-w-6xl mx-auto px-4 py-6 text-sm text-center text-gray-500">
        © {new Date().getFullYear()} Fadhili. Built with ❤️ in Kenya.
      </div>
    </footer>
  );
}
