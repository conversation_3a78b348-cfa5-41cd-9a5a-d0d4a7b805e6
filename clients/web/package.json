{"scripts": {"dev": "vike dev", "build": "vike build", "preview": "vike preview", "lint": "eslint .", "shadcn": "npx shadcn@latest"}, "dependencies": {"vike": "^0.4.236", "@vitejs/plugin-react": "^4.7.0", "react": "^19.1.0", "react-dom": "^19.1.0", "vike-react": "^0.6.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "lucide-react": "^0.526.0", "@radix-ui/react-icons": "^1.3.2", "tw-animate-css": "^1.3.6"}, "devDependencies": {"typescript": "^5.8.3", "vite": "^6.3.5", "eslint": "^9.32.0", "@eslint/js": "^9.32.0", "typescript-eslint": "^8.38.0", "globals": "^16.3.0", "eslint-plugin-react": "^7.37.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "tailwindcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11"}, "type": "module"}