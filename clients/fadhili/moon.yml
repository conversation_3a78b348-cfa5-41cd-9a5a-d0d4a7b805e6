$schema: '../../.moon/schema/project.json'

language: 'typescript'

tasks:
  dev:
    command: 'bun run dev'
    inputs:
      - 'src/**/*'
    options:
      cache: false

  build:
    command: 'bun run build'
    inputs:
      - 'src/**/*'
    options:
      cache: true

  # test:
  #   command: 'bun test'
  #   inputs:
  #     - 'src/**/*'
  #     - 'tests/**/*'
  #   options:
  #     cache: true
  ci:
    deps: 
      - ~:build
