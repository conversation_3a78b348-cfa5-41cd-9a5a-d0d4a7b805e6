{"name": "portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "bunx --bun vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@lottiefiles/dotlottie-react": "^0.14.4", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.11", "@web3auth/modal": "^10.0.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "buffer": "^6.0.3", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "process": "^0.11.10", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}, "packageManager": "bun@1.2.0"}