// Import polyfills first
import "./polyfills";

import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import { RouterProvider } from "react-router";
import { router } from "./routes";

import { Web3AuthProvider } from "@web3auth/modal/react";
import { createWeb3AuthConfig } from "./lib/web3authContext";

const web3AuthConfig = createWeb3AuthConfig();

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <Web3AuthProvider config={web3AuthConfig}>
      <RouterProvider router={router} />
    </Web3AuthProvider>
  </StrictMode>
);
