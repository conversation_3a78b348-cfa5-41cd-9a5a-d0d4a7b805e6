import { type Web3AuthContextConfig } from "@web3auth/modal/react";
import { WEB3AUTH_NETWORK } from "@web3auth/modal";

export const createWeb3AuthConfig = (): Web3AuthContextConfig => {
  const id = import.meta.env.VITE_WEB3AUTH_CLIENT_ID || "BIFTqoZDHpcmW1GCqWjw0aUFwa5byumHAGkSRlZDNIhM6-9P3Inv31GWplsNab_zkPz_6TLnz4Tq4XHFfdNOGd0"

  console.log("ID ", id);

  return {
    web3AuthOptions: {
      clientId: id,
      web3AuthNetwork: WEB3AUTH_NETWORK.SAPPHIRE_DEVNET,
    },
  };
};