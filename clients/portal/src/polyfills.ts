// Polyfills for Web3Auth and crypto libraries
import { <PERSON><PERSON><PERSON> } from 'buffer';

// Set up global polyfills for browser environment
if (typeof window !== 'undefined') {
  // Buffer polyfill
  (window as any).Buffer = Buffer;
  
  // Process polyfill
  (window as any).process = {
    env: {},
    version: '',
    versions: {},
    platform: 'browser',
    browser: true,
    nextTick: (fn: Function) => setTimeout(fn, 0),
  };
  
  // Global polyfill
  (window as any).global = window;
}

export {};
