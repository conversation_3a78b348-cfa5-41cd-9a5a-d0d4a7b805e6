import { Button } from "@/components/ui/button";
import { useWeb3Auth } from "@web3auth/modal/react";
import { useState, useEffect } from "react";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";

export const AuthPage = () => {
  const { web3Auth, isConnected } = useWeb3Auth();
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const getUserInfo = async () => {
      if (web3Auth && isConnected) {
        try {
          const userInfo = await web3Auth.getUserInfo();
          console.log("User info:", userInfo);  
          setUser(userInfo);
        } catch (error) {
          console.error("Failed to get user info:", error);
        }
      } else {
        setUser(null);
      }
    };

    getUserInfo();
  }, [web3Auth, isConnected]);

  const handleLogin = async () => {
    try {
      if (web3Auth) {
        await web3Auth.connect();
      }
    } catch (error) {
      console.error("Login failed:", error);
    }
  };

  const handleLogout = async () => {
    try {
      if (web3Auth) {
        await web3Auth.logout();
      }
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <>
      <div className="fixed inset-0 -z-10 w-screen h-screen overflow-hidden">
        <DotLottieReact
          src="https://lottie.host/8a4d2c22-e8f8-4c23-9b21-925bab4730bb/fQmn1KLMmP.lottie"
          autoplay
          loop
          speed={1}
          style={{ width: "100%", height: "100%", objectFit: "cover" }}
        />
      </div>
      <div className="w-screen h-screen flex justify-center items-center bg-transparent">
        <div className="flex flex-col items-center gap-4">
          {!isConnected ? (
            <Button onClick={handleLogin}>
              Login with Web3Auth
            </Button>
          ) : (
            <div className="flex flex-col items-center gap-4">
              <p className="text-sm text-gray-600">
                Welcome, {user?.name || user?.email || "User"}!
              </p>
              <Button onClick={handleLogout} variant="outline">
                Logout
              </Button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
