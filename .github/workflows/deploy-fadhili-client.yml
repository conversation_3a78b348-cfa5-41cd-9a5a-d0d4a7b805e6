name: Deploy changed apps (Bun + Moon + Firebase)

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  

      - name: Setup Moon CLI + Toolchain via Proto
        uses: moonrepo/setup-toolchain@v0 
  
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          token_format: access_token
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github-actions-pool/providers/github-provider"
          service_account: "<EMAIL>"

      - name: Build changed apps
        run: |
          echo "Running Moon CI and building affected apps j..."
          moon run :ci
          moon run --affected :build -- --scope clients/*

      # - name: Install Firebase CLI
      #   run: bun install firebase-tools
      
      - name: Deploy only changed apps to Firebase
        run: |
          CHANGED=$( moon query projects --affected --json | grep -E '^\s*"source":' | sed -E 's/.*"source": ?"([^"]+)".*/\1/')

          echo $CHANGED

          if echo "$CHANGED" | grep -q '^clients/web$'; then
            bunx firebase deploy --only hosting:web --project "$FIREBASE_PROJECT_ID"
          fi
          if echo "$CHANGED" | grep -q '^clients/portal$'; then
            bunx firebase deploy --only hosting:portal --project "$FIREBASE_PROJECT_ID"
          fi
          if echo "$CHANGED" | grep -q '^clients/fadhili$'; then
            bunx firebase deploy --only hosting:admin --project "$FIREBASE_PROJECT_ID"
          fi
        env:
          FIREBASE_PROJECT_ID: fadhili-project-001

      - name: Cache Moon state
        uses: actions/cache@v4
        with:
          path: ~/.cache/moon
          key: moon-${{ runner.os }}-${{ hashFiles('.moon/toolchain.yml') }}
