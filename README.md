# fadhili-repo

To run this project:

1. Install [Proton](https://moonrepo.dev/docs/proton/install).
2. Run the following command to install other dependencies and set up the project:

    ```sh
    moon run ci
    ```

## Frontend

The frontend consists of three clients:

- `client/fadhili` - fadhili
- `client/web`  - web
- `client/portal` - portal

To run, build, or test a specific client, use the following commands (replace `<client>` with `fadhili`, `web`, or `portal`):

- **Install dependencies:**
    ```sh
    bun install
    ```
- **Run development server:**
    ```sh
    moon run dev:<client>
    ```
- **Build:**
    ```sh
    moon run build:<client>
    ```
- **Test:**
    ```sh
    moon run test:<client>
    ```

- **dev for web example:**
    ```sh
    moon run dev:web
    ```